import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import {
  LayoutDashboard,
  Home,
  Users,
  Link as LinkIcon,
  DollarSign,
  User,
  LogOut,
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  HelpCircle,
  Bell,
  X,
  Grid,
  Heart,
  Settings as SettingsIcon,
  CreditCard,
  Truck,
  UserCheck,
  Package2,
  Layers, // Added for Produits semi
  ListChecks, // Added for BOM Produits
  MapPin, // Added for Locations
  FileText, // Added for Fiche d'accompagnement
  ArrowUpRight, // Added for Sortie
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetClose } from "@/components/ui/sheet";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface SidebarProps {
  className?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

const Sidebar = ({ className, isOpen = false, onClose }: SidebarProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [pendingOrdersCount, setPendingOrdersCount] = useState(0);
  const [pendingPaymentsCount, setPendingPaymentsCount] = useState(0);

  // Fetch pending counts on component mount
  useEffect(() => {
    const fetchPendingCounts = async () => {
      try {
        // Removed getPendingOrdersCount and getPendingPaymentsCount
      } catch (error) {
        console.error('Failed to fetch pending counts:', error);
      }
    };

    fetchPendingCounts();
  }, []);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success('Logged out successfully');
      navigate('/login');
    } catch (error) {
      toast.error('Failed to logout. Please try again.');
    }
  };

  // Render mobile sidebar using Sheet component
  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="left" className="p-0 w-[280px] sm:w-[320px]">
          <div className="flex flex-col h-full bg-sidebar text-sidebar-foreground">
            {/* Mobile Sidebar Header */}
            <div className="flex items-center justify-between p-4 ">
              <div className="flex items-center gap-3">
                <img
                  src="/friction.png"
                  alt="Logo"
                  className="w-10 h-10 object-cover"
                />
              </div>
              <SheetClose asChild>
                <Button variant="ghost" size="icon" className="text-muted-foreground">
                  <X size={18} />
                </Button>
              </SheetClose>
            </div>

            {/* Mobile Navigation Links */}
            <div className="flex-1 py-4 overflow-y-auto">
              <nav className="px-2 space-y-1">
                <MobileNavItem to="/" icon={<Home size={20} />} label="Dashboard" />
                <MobileNavItem to="/products" icon={<ShoppingBag size={20} />} label="Matières premières" />
                <MobileNavItem to="/semi-products" icon={<Layers size={20} />} label="Produits semi" />
                <MobileNavItem to="/finished-products" icon={<Package2 size={20} />} label="Produits finis" />
                <MobileNavItem to="/articles" icon={<ListChecks size={20} />} label="Liste d'articles" />
                <MobileNavItem to="/control-quality" icon={<CreditCard size={20} />} label="Contrôle de qualité" />
                <MobileNavItem to="/fiche-accompagnement" icon={<FileText size={20} />} label="Fiche d'accompagnement" />
                <MobileNavItem to="/sortie" icon={<ArrowUpRight size={20} />} label="Sortie Employeurs" />
                <MobileNavItem to="/fournisseur" icon={<Users size={20} />} label="Fournisseur" />
                <MobileNavItem to="/staff" icon={<UserCheck size={20} />} label="Staff" />
                <MobileNavItem to="/locations" icon={<MapPin size={20} />} label="Locations" />
                <MobileNavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" />
              </nav>
            </div>

            {/* Mobile Footer */}
            <div className="p-4 border-t border-border">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground justify-start gap-3"
              >
                <LogOut size={20} />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop sidebar
  return (
    <div className={cn(
      "relative h-screen flex flex-col bg-sidebar text-sidebar-foreground border-r border-border transition-all duration-300 hidden md:flex",
      collapsed ? "w-20" : "w-64",
      className
    )}>
      {/* Logo */}
      <div className="relative flex flex-col items-center justify-center p-4 border-b border-border">
        <div className="flex items-center justify-center w-full">
          <img
            src="/friction.png"
            alt="Logo"
            className="w-10 h-10 object-cover"
            style={{ maxWidth: '40px', maxHeight: '40px' }}
          />
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className={cn("absolute -right-3 top-1/2 -translate-y-1/2 bg-background border border-border rounded-full shadow-sm text-muted-foreground", collapsed && "rotate-180")}
        >
          {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
        </Button>
      </div>

      {/* Navigation Links */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-2 space-y-1">
          <NavItem to="/" icon={<Home size={20} />} label="Dashboard" collapsed={collapsed} />
          <NavItem to="/products" icon={<ShoppingBag size={20} />} label="Matières premières" collapsed={collapsed} />
          <NavItem to="/semi-products" icon={<Layers size={20} />} label="Produits semi" collapsed={collapsed} />
          <NavItem to="/finished-products" icon={<Package2 size={20} />} label="Produits finis" collapsed={collapsed} />
          <NavItem to="/articles" icon={<ListChecks size={20} />} label="Liste d'articles" collapsed={collapsed} />
          <NavItem to="/control-quality" icon={<CreditCard size={20} />} label="Contrôle de qualité" collapsed={collapsed} />
          <NavItem to="/sortie" icon={<ArrowUpRight size={20} />} label="Sortie Employeurs" collapsed={collapsed} />
          <NavItem to="/fiche-accompagnement" icon={<FileText size={20} />} label="Fiche d'accompagnement" collapsed={collapsed} />
          <NavItem to="/fournisseur" icon={<Users size={20} />} label="Fournisseur" collapsed={collapsed} />
          <NavItem to="/staff" icon={<UserCheck size={20} />} label="Staff" collapsed={collapsed} />
          <NavItem to="/locations" icon={<MapPin size={20} />} label="Locations" collapsed={collapsed} />
          <NavItem to="/settings" icon={<SettingsIcon size={20} />} label="Settings" collapsed={collapsed} />
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <button
          onClick={handleLogout}
          className={cn(
            "flex items-center w-full px-3 py-2 text-sm rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
            !collapsed && "justify-start gap-3",
            collapsed && "justify-center"
          )}
        >
          <LogOut size={20} />
          {!collapsed && <span>Logout</span>}
        </button>
      </div>
    </div>
  );
};

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
  count?: number;
}

const NavItem = ({ to, icon, label, collapsed, count }: NavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground",
      !collapsed && "justify-start gap-3",
      collapsed && "justify-center"
    )}
  >
    {icon}
    {!collapsed && (
      <div className="flex items-center w-full">
        <div className="flex items-center gap-2">
          <span>{label}</span>
          {typeof count === 'number' && count > 0 && (
            <span className="inline-flex items-center justify-center bg-red-500 text-white text-[10px] font-semibold rounded-full w-4 h-4 ml-1">
              {count}
            </span>
          )}
        </div>
      </div>
    )}
    {collapsed && typeof count === 'number' && count > 0 && (
      <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[8px] px-1 py-0.5 rounded-full min-w-[12px] text-center font-medium">
        {count}
      </span>
    )}
  </Link>
);

// Mobile navigation item component
interface MobileNavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  count?: number;
}

const MobileNavItem = ({ to, icon, label, count }: MobileNavItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center px-3 py-2 text-sm rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground relative",
      to === window.location.pathname && "bg-sidebar-accent text-sidebar-accent-foreground",
      "justify-start gap-3"
    )}
  >
    {icon}
    <div className="flex items-center w-full">
      <div className="flex items-center gap-2">
        <span>{label}</span>
        {typeof count === 'number' && count > 0 && (
          <span className="inline-flex items-center justify-center bg-red-500 text-white text-[10px] font-semibold rounded-full w-4 h-4 ml-1">
            {count}
          </span>
        )}
      </div>
    </div>
  </Link>
);

export default Sidebar;


